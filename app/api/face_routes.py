from fastapi import APIRouter, HTTPException, UploadFile
from fastapi.responses import JSONResponse
from pydantic import HttpUrl

from app.logging import logger

face_router = APIRouter(prefix="/face")


@face_router.get("/validate")
async def validate_face(fileId: str, fileUrl: HttpUrl) -> JSONResponse:
    logger.info(f"Validating face for file {fileId} from {fileUrl}")
    return JSONResponse({"message": "Face validation successful"})
