from typing import <PERSON>ple

from app.helpers import is_valid_image_url, download_image_to_memory, detect_faces_in_image
from app.logging import logger
from app.models import FaceValidationResponse


class FaceValidationService:
    """Service for validating faces in images from URLs."""
    
    def __init__(self):
        """Initialize the face validation service."""
        logger.info("Face validation service initialized")
    
    async def validate_face_from_url(self, file_url: str, file_id: str) -> FaceValidationResponse:
        """
        Validate if an image from a URL contains faces.
        
        Args:
            file_url: URL of the image to validate
            file_id: ID of the file (for logging purposes)
            
        Returns:
            FaceValidationResponse with validation result
        """
        logger.info(f"Starting face validation for file {file_id} from URL: {file_url}")
        
        try:
            # Step 1: Validate URL
            is_valid, url_error = is_valid_image_url(file_url)
            if not is_valid:
                logger.warning(f"URL validation failed for {file_id}: {url_error}")
                return FaceValidationResponse(
                    has_face=False,
                    error=f"URL validation failed: {url_error}"
                )
            
            logger.info(f"URL validation successful for {file_id}")
            
            # Step 2: Download image
            image_array, download_error = download_image_to_memory(file_url)
            if image_array is None:
                logger.warning(f"Image download failed for {file_id}: {download_error}")
                return FaceValidationResponse(
                    has_face=False,
                    error=f"Image download failed: {download_error}"
                )
            
            logger.info(f"Image download successful for {file_id}")
            
            # Step 3: Detect faces
            has_faces, detection_error = detect_faces_in_image(image_array)
            if detection_error:
                logger.error(f"Face detection failed for {file_id}: {detection_error}")
                return FaceValidationResponse(
                    has_face=False,
                    error=f"Face detection failed: {detection_error}"
                )
            
            # Step 4: Return result
            result_message = f"Face validation completed for {file_id}: {'faces detected' if has_faces else 'no faces detected'}"
            logger.info(result_message)
            
            return FaceValidationResponse(
                has_face=has_faces,
                message=result_message
            )
            
        except Exception as e:
            error_msg = f"Unexpected error during face validation for {file_id}: {str(e)}"
            logger.error(error_msg)
            return FaceValidationResponse(
                has_face=False,
                error=error_msg
            )


# Global service instance
face_validation_service = FaceValidationService()
