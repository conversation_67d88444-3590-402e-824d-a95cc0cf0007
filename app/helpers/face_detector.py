import cv2
import numpy as np
from typing import <PERSON><PERSON>, List
import os

from app.logging import logger


class FaceDetector:
    """Face detector using OpenCV Haar cascades for CPU-only detection."""
    
    def __init__(self):
        """Initialize the face detector with Haar cascade classifier."""
        # Load the pre-trained Haar cascade for face detection
        cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
        
        if not os.path.exists(cascade_path):
            raise FileNotFoundError(f"Haar cascade file not found: {cascade_path}")
        
        self.face_cascade = cv2.CascadeClassifier(cascade_path)
        
        if self.face_cascade.empty():
            raise RuntimeError("Failed to load Haar cascade classifier")
        
        logger.info("Face detector initialized successfully")
    
    def detect_faces(self, image: np.ndarray) -> Tuple[bool, List[Tuple[int, int, int, int]], str | None]:
        """
        Detect faces in an image.
        
        Args:
            image: Input image as numpy array (RGB format)
            
        Returns:
            Tuple of (has_faces, face_rectangles, error_message)
            face_rectangles is a list of (x, y, width, height) tuples
        """
        try:
            if image is None or image.size == 0:
                return False, [], "Invalid or empty image"
            
            # Convert RGB to BGR for OpenCV
            if len(image.shape) == 3 and image.shape[2] == 3:
                image_bgr = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            else:
                return False, [], "Image must be in RGB format"
            
            # Convert to grayscale for face detection
            gray = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2GRAY)
            
            # Detect faces
            faces = self.face_cascade.detectMultiScale(
                gray,
                scaleFactor=1.1,      # How much the image size is reduced at each scale
                minNeighbors=5,       # How many neighbors each candidate rectangle should have to retain it
                minSize=(30, 30),     # Minimum possible face size
                flags=cv2.CASCADE_SCALE_IMAGE
            )
            
            # Convert numpy array to list of tuples
            face_rectangles = [(int(x), int(y), int(w), int(h)) for x, y, w, h in faces]
            
            has_faces = len(face_rectangles) > 0
            
            if has_faces:
                logger.info(f"Detected {len(face_rectangles)} face(s) in image")
            else:
                logger.info("No faces detected in image")
            
            return has_faces, face_rectangles, None
            
        except Exception as e:
            error_msg = f"Face detection error: {str(e)}"
            logger.error(error_msg)
            return False, [], error_msg


def detect_faces_in_image(image: np.ndarray) -> Tuple[bool, str | None]:
    """
    Convenience function to detect if an image contains faces.
    
    Args:
        image: Input image as numpy array (RGB format)
        
    Returns:
        Tuple of (has_faces, error_message)
    """
    try:
        detector = FaceDetector()
        has_faces, _, error = detector.detect_faces(image)
        return has_faces, error
    except Exception as e:
        error_msg = f"Failed to initialize face detector: {str(e)}"
        logger.error(error_msg)
        return False, error_msg
